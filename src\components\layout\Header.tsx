
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import Logo from '@/components/Logo';
import { UserCircle, LogIn } from 'lucide-react';

interface HeaderProps {
  showAuthButtons?: boolean;
}

const Header = ({ showAuthButtons = true }: HeaderProps) => {
  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Logo size="md" />
          
          {showAuthButtons && (
            <div className="flex items-center space-x-4">
              <Button asChild variant="outline" size="sm">
                <Link to="/login">
                  <LogIn className="w-4 h-4 mr-2" />
                  Sign In
                </Link>
              </Button>
              <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Link to="/signup">
                  <UserCircle className="w-4 h-4 mr-2" />
                  Sign Up
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
